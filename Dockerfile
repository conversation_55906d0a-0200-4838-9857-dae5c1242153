# 基于官方 PHP 8.1 镜像构建
FROM php:8.1-apache

# 安装必要的系统依赖和 PHP 扩展
RUN apt-get update && apt-get install -y \
    libfreetype-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libzip-dev \
    libssl-dev \
    openssl \
    netcat-traditional \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd pdo pdo_mysql zip sockets \
    && rm -rf /var/lib/apt/lists/*

# 注意：应用代码通过 docker-compose.yml 中的 volumes 挂载
# 这样可以确保运行时修改的文件持久化到本地磁盘

# 复制入口脚本
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 设置 Apache 配置
RUN a2enmod rewrite

# 设置默认环境变量
ENV DB_DRIVER=mysql
ENV DB_HOST=localhost:3306
ENV DB_DATABASE=defaultdb
ENV DB_USERNAME=root
ENV DB_PASSWORD=password
ENV DB_CHARSET=utf8mb4
ENV DB_COLLATION=utf8mb4_unicode_ci
ENV DB_PREFIX=acg_

# 设置工作目录
WORKDIR /acg-faka

# 暴露端口
EXPOSE 80

# 设置入口点
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["apache2-foreground"]
