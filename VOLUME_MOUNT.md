# ACG-FAKA 本地磁盘挂载配置

## 配置说明

本项目已配置 acg-faka 文件夹的本地磁盘挂载，确保运行时修改的文件能够持久化到本地磁盘。

## 挂载配置

### Docker Compose 配置
在 `docker-compose.yml` 中配置了以下挂载：

```yaml
volumes:
  # 挂载整个 acg-faka 目录，确保运行时修改的文件持久化到本地磁盘
  - ./acg-faka:/var/www/html
```

### Dockerfile 优化
- 移除了 `COPY acg-faka /var/www/html` 命令
- 完全依赖 docker-compose 的挂载配置
- 通过 docker-entrypoint.sh 设置正确的文件权限

## 优势

1. **持久化存储**：运行时生成或修改的文件会保存到本地磁盘
2. **开发便利**：本地修改代码可以立即生效，无需重新构建镜像
3. **数据安全**：容器重启或删除不会丢失重要文件
4. **配置灵活**：可以直接在本地修改配置文件

## 重要目录说明

- `acg-faka/runtime/` - 运行时缓存和临时文件
- `acg-faka/config/` - 应用配置文件
- `acg-faka/assets/cache/` - 静态资源缓存
- `acg-faka/runtime.log` - 应用日志文件

## 使用注意事项

1. 确保本地 `acg-faka` 目录存在且有正确的权限
2. 容器启动时会自动设置 `www-data:www-data` 权限
3. 修改配置文件后可能需要重启容器以生效
4. 备份重要数据时记得包含整个 `acg-faka` 目录
