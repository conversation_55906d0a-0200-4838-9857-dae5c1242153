version: '3'
services:
  acg_faka:
    image: acg_faka:latest
    container_name: ${DOCKER_FAKA_CONTAINER_NAME}
    build:
      context: .
    # ports:
    #   - 8080:80
    environment:
      - DB_DRIVER=mysql
      - DB_HOST=acg_faka_db:3306
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_CHARSET=utf8mb4
      - DB_COLLATION=utf8mb4_unicode_ci
      - DB_PREFIX=acg_
    volumes:
      # 挂载整个 acg-faka 目录，确保运行时修改的文件持久化到本地磁盘
      - ./acg-faka:/acg-faka
    depends_on:
      - acg_faka_db
    networks:
      - acg_faka
  acg_faka_db:
    image: mysql:8
    container_name: ${DOCKER_FAKA_DB_CONTAINER_NAME}
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - ./data:/var/lib/mysql
    networks:
      - acg_faka

networks:
  acg_faka:
    name: ${DOCKER_FAKA_NETWORKNAME}
    external: ${DOCKER_FAKA_EXTERNAL_NETWORK}
